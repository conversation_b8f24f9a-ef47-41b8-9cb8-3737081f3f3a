import { useState } from "react";
import { Plus, Star } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import wingsImage from "@/assets/wings-buffalo.jpg";
import burgerImage from "@/assets/burger-deluxe.jpg";

const Menu = () => {
  const [activeCategory, setActiveCategory] = useState("wings");

  const categories = [
    { id: "wings", name: "Wings", count: 12 },
    { id: "burgers", name: "Burgers", count: 8 },
    { id: "sides", name: "Sides", count: 6 },
    { id: "drinks", name: "Drinks", count: 10 },
    { id: "desserts", name: "Desserts", count: 4 },
  ];

  const menuItems = {
    wings: [
      {
        id: 1,
        name: "Classic Buffalo Wings",
        description: "Traditional buffalo wings with our signature sauce, served with celery and blue cheese dip",
        price: 12.99,
        image: wingsImage,
        spicy: "Medium",
        popular: true,
        rating: 4.8
      },
      {
        id: 2,
        name: "Honey BBQ Wings",
        description: "Sweet and smoky BBQ wings glazed with premium honey",
        price: 13.99,
        image: wingsImage,
        spicy: "Mild",
        popular: false,
        rating: 4.6
      },
      {
        id: 3,
        name: "Ghost Pepper Wings",
        description: "For the brave souls - wings coated in our legendary ghost pepper sauce",
        price: 15.99,
        image: wingsImage,
        spicy: "Extreme",
        popular: true,
        rating: 4.9
      },
      {
        id: 4,
        name: "Lemon Pepper Wings",
        description: "Crispy wings seasoned with zesty lemon pepper blend",
        price: 12.99,
        image: wingsImage,
        spicy: "None",
        popular: false,
        rating: 4.5
      }
    ],
    burgers: [
      {
        id: 5,
        name: "King Ping Deluxe",
        description: "Double beef patty, aged cheddar, bacon, lettuce, tomato, special sauce",
        price: 16.99,
        image: burgerImage,
        popular: true,
        rating: 4.7
      },
      {
        id: 6,
        name: "BBQ Bacon Burger",
        description: "Beef patty, BBQ sauce, crispy bacon, onion rings, cheddar cheese",
        price: 15.99,
        image: burgerImage,
        popular: false,
        rating: 4.5
      }
    ]
  };

  const getSpicyColor = (level: string) => {
    switch (level) {
      case "None": return "bg-green-500";
      case "Mild": return "bg-yellow-500";
      case "Medium": return "bg-orange-500";
      case "Hot": return "bg-red-500";
      case "Extreme": return "bg-purple-500";
      default: return "bg-gray-500";
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-gradient-dark">
        <div className="container mx-auto px-4 lg:px-8 text-center">
          <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-fade-in">
            Our <span className="text-primary">Menu</span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto animate-slide-up">
            Discover our legendary wings, gourmet burgers, and more. Every dish is crafted with 
            passion and served with pride.
          </p>
        </div>
      </section>

      {/* Categories */}
      <section className="py-8 bg-secondary sticky top-20 z-40">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                  activeCategory === category.id
                    ? "bg-primary text-primary-foreground shadow-glow"
                    : "bg-card text-foreground hover:bg-primary/10 hover:text-primary"
                }`}
              >
                {category.name} ({category.count})
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Menu Items */}
      <section className="py-16">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {(menuItems[activeCategory as keyof typeof menuItems] || []).map((item: any, index: number) => (
              <Card 
                key={item.id} 
                className="card-menu-item group animate-fade-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="relative">
                  <img 
                    src={item.image} 
                    alt={item.name}
                    className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute top-4 left-4 flex gap-2">
                    {item.popular && (
                      <Badge className="bg-primary text-primary-foreground">
                        Popular
                      </Badge>
                    )}
                    {'spicy' in item && item.spicy !== "None" && (
                      <Badge className={`text-white ${getSpicyColor(item.spicy)}`}>
                        {item.spicy}
                      </Badge>
                    )}
                  </div>
                  <div className="absolute top-4 right-4 bg-black/80 text-white px-2 py-1 rounded-full text-sm flex items-center gap-1">
                    <Star className="h-3 w-3 fill-current text-yellow-400" />
                    {item.rating}
                  </div>
                </div>
                
                <div className="p-6">
                  <h3 className="text-xl font-playfair font-bold mb-2 group-hover:text-primary transition-colors duration-300">
                    {item.name}
                  </h3>
                  <p className="text-muted-foreground mb-4 leading-relaxed">
                    {item.description}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold text-primary">
                      ${item.price}
                    </span>
                    <Button className="btn-primary">
                      <Plus className="h-4 w-4 mr-2" />
                      Add to Cart
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          {(!menuItems[activeCategory as keyof typeof menuItems] || 
            menuItems[activeCategory as keyof typeof menuItems]?.length === 0) && (
            <div className="text-center py-16">
              <p className="text-xl text-muted-foreground">
                Coming soon! We're working on expanding this section.
              </p>
            </div>
          )}
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Menu;
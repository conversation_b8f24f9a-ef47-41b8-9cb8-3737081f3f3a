@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* King Ping Wings Design System - Premium Black & Yellow Theme */

@layer base {
  :root {
    /* Core Brand Colors */
    --background: 0 0% 4%;
    --foreground: 45 100% 95%;
    
    /* Premium Black & Yellow Palette */
    --primary: 45 100% 51%;          /* Vibrant Yellow */
    --primary-foreground: 0 0% 4%;   /* Deep Black */
    
    --secondary: 0 0% 12%;           /* Charcoal Black */
    --secondary-foreground: 45 100% 95%;
    
    --accent: 45 100% 45%;           /* Golden Yellow */
    --accent-foreground: 0 0% 4%;
    
    --muted: 0 0% 8%;                /* Dark Grey */
    --muted-foreground: 0 0% 60%;
    
    /* UI Elements */
    --card: 0 0% 6%;
    --card-foreground: 45 100% 95%;
    
    --popover: 0 0% 6%;
    --popover-foreground: 45 100% 95%;
    
    --border: 0 0% 15%;
    --input: 0 0% 10%;
    --ring: 45 100% 51%;
    
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    
    --radius: 0.75rem;
    
    /* Premium Gradients & Advanced Effects */
    --gradient-primary: linear-gradient(135deg, hsl(45 100% 51%), hsl(45 100% 45%));
    --gradient-dark: linear-gradient(135deg, hsl(0 0% 4%), hsl(0 0% 8%));
    --gradient-hero: linear-gradient(135deg, hsl(0 0% 4%) 0%, hsl(0 0% 8%) 100%);
    --gradient-luxury: linear-gradient(135deg, hsl(45 100% 51%) 0%, hsl(35 100% 50%) 50%, hsl(25 100% 45%) 100%);
    --gradient-text: linear-gradient(135deg, hsl(45 100% 60%), hsl(45 100% 40%));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 6%) 0%, hsl(0 0% 10%) 100%);
    --gradient-spotlight: radial-gradient(circle at center, hsl(45 100% 51% / 0.1) 0%, transparent 70%);
    
    /* Advanced Shadows & Effects */
    --shadow-glow: 0 0 30px hsl(45 100% 51% / 0.2);
    --shadow-premium: 0 25px 50px -12px hsl(0 0% 0% / 0.8);
    --shadow-card: 0 10px 25px -3px hsl(0 0% 0% / 0.3);
    --shadow-luxury: 0 0 60px hsl(45 100% 51% / 0.3);
    --shadow-text: 0 0 20px hsl(45 100% 51% / 0.5);
    --shadow-inset: inset 0 1px 0 hsl(45 100% 51% / 0.1);
    
    /* Advanced Animation Timings */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-elastic: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-luxury: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter antialiased;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-playfair;
  }
}

@layer components {
  /* Luxury Button Variants */
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-accent font-bold px-10 py-5 rounded-xl 
           transition-all duration-500 hover:scale-110 hover:shadow-luxury 
           relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-luxury
           before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-500;
  }
  
  .btn-secondary {
    @apply bg-secondary text-secondary-foreground border-2 border-primary/30 hover:border-primary
           font-bold px-10 py-5 rounded-xl transition-all duration-500 hover:scale-110
           hover:shadow-glow relative overflow-hidden;
  }
  
  .btn-ghost {
    @apply text-primary hover:bg-primary/20 font-bold px-8 py-4 rounded-xl
           transition-all duration-500 hover:scale-105 hover:shadow-glow;
  }
  
  .btn-luxury {
    @apply bg-gradient-luxury text-black font-black px-12 py-6 rounded-2xl
           transition-all duration-700 hover:scale-125 hover:shadow-luxury
           hover:rotate-1 transform-gpu;
  }
  
  /* Spectacular Text Styles */
  .hero-title {
    @apply text-7xl md:text-9xl font-playfair font-black text-foreground leading-none
           bg-gradient-text bg-clip-text text-transparent animate-pulse-slow;
  }
  
  .hero-subtitle {
    @apply text-2xl md:text-3xl text-muted-foreground font-light leading-relaxed;
  }
  
  .section-title {
    @apply text-5xl md:text-7xl font-playfair font-black leading-tight;
  }
  
  .luxury-text {
    @apply bg-gradient-luxury bg-clip-text text-transparent font-black;
  }
  
  /* Advanced Card Components */
  .card-premium {
    @apply bg-gradient-card border border-border rounded-2xl p-8 hover:border-primary/50
           transition-all duration-700 hover:scale-110 hover:-rotate-1 shadow-premium
           hover:shadow-luxury transform-gpu;
  }
  
  .card-menu-item {
    @apply bg-card border border-border rounded-xl overflow-hidden
           hover:border-primary/50 transition-all duration-300 hover:scale-105 shadow-card
           cursor-pointer;
  }
  
  .card-testimonial {
    @apply bg-gradient-card border-2 border-primary/20 rounded-3xl p-10
           hover:border-primary hover:scale-105 transition-all duration-500
           shadow-premium hover:shadow-luxury transform-gpu;
  }
  
  .card-feature {
    @apply bg-gradient-card backdrop-blur-lg rounded-2xl p-8 border border-primary/30
           hover:border-primary transition-all duration-500 hover:scale-110
           shadow-card hover:shadow-luxury transform-gpu;
  }
  
  /* Spectacular Gradient Backgrounds */
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }
  
  .bg-gradient-dark {
    background: var(--gradient-dark);
  }
  
  .bg-gradient-hero {
    background: var(--gradient-hero);
  }
  
  .bg-gradient-luxury {
    background: var(--gradient-luxury);
  }
  
  .bg-gradient-card {
    background: var(--gradient-card);
  }
  
  .bg-gradient-text {
    background: var(--gradient-text);
  }
  
  /* Mind-Blowing Animation Classes */
  .animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
  }
  
  .animate-slide-up {
    animation: slideUp 1s ease-out forwards;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.7s ease-out forwards;
  }
  
  .animate-glow {
    animation: glow 3s ease-in-out infinite alternate;
  }
  
  .animate-pulse-slow {
    animation: pulseSlow 4s ease-in-out infinite;
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-rotate-slow {
    animation: rotateSlow 20s linear infinite;
  }
  
  .animate-bounce-slow {
    animation: bounceSlow 3s ease-in-out infinite;
  }
  
  .animate-shimmer {
    animation: shimmer 2s ease-in-out infinite;
  }
  
  /* Advanced Hover Effects */
  .hover-lift {
    @apply transition-transform duration-500 hover:scale-110 hover:-translate-y-4
           hover:rotate-1 transform-gpu;
  }
  
  .hover-glow {
    @apply transition-all duration-500 hover:shadow-luxury;
  }
  
  .hover-expand {
    @apply transition-all duration-700 hover:scale-125 hover:rotate-3 transform-gpu;
  }
  
  .hover-luxury {
    @apply transition-all duration-700 hover:scale-110 hover:shadow-luxury 
           hover:border-primary/80 transform-gpu;
  }
}

/* Advanced Keyframe Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(40px) scale(0.9); }
  to { opacity: 1; transform: translateY(0) scale(1); }
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(60px) rotateX(15deg); }
  to { opacity: 1; transform: translateY(0) rotateX(0deg); }
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.8) rotate(-5deg); }
  to { opacity: 1; transform: scale(1) rotate(0deg); }
}

@keyframes glow {
  from { box-shadow: 0 0 30px hsl(45 100% 51% / 0.2); }
  to { box-shadow: 0 0 60px hsl(45 100% 51% / 0.6); }
}

@keyframes pulseSlow {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(1deg); }
  66% { transform: translateY(-10px) rotate(-1deg); }
}

@keyframes rotateSlow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes bounceSlow {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-30px); }
}

@keyframes shimmer {
  0% { background-position: -1000px 0; }
  100% { background-position: 1000px 0; }
}
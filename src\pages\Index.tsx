import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { ArrowR<PERSON>, Star, Clock, Truck, ChefHat, Award, Play, Quote, Users, Heart, Sparkles, Trophy, Flame, Crown } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import heroImage from "@/assets/hero-wings.jpg";
import wingsImage from "@/assets/wings-buffalo.jpg";
import burgerImage from "@/assets/burger-deluxe.jpg";
import interiorImage from "@/assets/restaurant-interior.jpg";
import chefImage from "@/assets/chef-portrait.jpg";
import awardsImage from "@/assets/awards-display.jpg";
import cookingImage from "@/assets/cooking-process.jpg";
import customersImage from "@/assets/happy-customers.jpg";

const Index = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [statsVisible, setStatsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
    const timer = setTimeout(() => setStatsVisible(true), 1000);
    return () => clearTimeout(timer);
  }, []);

  const stats = [
    { number: "25+", label: "Years of Excellence", icon: Trophy },
    { number: "50K+", label: "Happy Customers", icon: Heart },
    { number: "500+", label: "Wings Served Daily", icon: Flame },
    { number: "25", label: "Signature Sauces", icon: Crown }
  ];

  const features = [
    {
      icon: ChefHat,
      title: "Master Craftsmanship",
      description: "Every wing is hand-crafted by our master chefs using time-honored techniques and premium ingredients sourced globally.",
      color: "from-orange-500 to-red-500"
    },
    {
      icon: Clock,
      title: "Lightning Fast Service",
      description: "Revolutionary kitchen technology ensures your order arrives piping hot in record time without compromising quality.",
      color: "from-blue-500 to-purple-500"
    },
    {
      icon: Star,
      title: "Flavor Innovation",
      description: "Our culinary laboratory continuously creates mind-blowing flavor combinations that redefine what wings can be.",
      color: "from-yellow-500 to-orange-500"
    },
    {
      icon: Award,
      title: "Award-Winning Excellence",
      description: "Recognized worldwide with prestigious culinary awards for taste, innovation, and exceptional dining experience.",
      color: "from-green-500 to-teal-500"
    }
  ];

  const testimonials = [
    {
      name: "Sarah Johnson",
      role: "Food Critic",
      content: "Absolutely revolutionary! King Ping Wings has redefined what premium casual dining means. Every bite is an explosion of perfectly balanced flavors.",
      rating: 5,
      image: customersImage
    },
    {
      name: "Marcus Chen",
      role: "Celebrity Chef",
      content: "The attention to detail and flavor innovation here is extraordinary. This is what happens when passion meets perfection.",
      rating: 5,
      image: chefImage
    },
    {
      name: "Emma Rodriguez",
      role: "Food Blogger",
      content: "I've traveled the world tasting wings, and nothing comes close to the culinary artistry at King Ping Wings. Simply magnificent!",
      rating: 5,
      image: customersImage
    }
  ];

  const cuisineHighlights = [
    {
      title: "Legendary Buffalo Wings",
      description: "Our signature creation with 72-hour marinated premium wings, secret spice blend, and artisanal buffalo sauce",
      image: wingsImage,
      price: "Starting at $16.99",
      popular: true,
      spicy: "Medium Heat"
    },
    {
      title: "Gourmet Beast Burger",
      description: "Triple-stacked wagyu beef patties with truffle aioli, aged cheddar, and handcrafted brioche bun",
      image: burgerImage,
      price: "Starting at $24.99",
      popular: true,
      spicy: null
    },
    {
      title: "Fire Dragon Wings",
      description: "Extreme heat challenge wings with Carolina Reaper sauce - only for the bravest souls",
      image: wingsImage,
      price: "Starting at $19.99",
      popular: false,
      spicy: "Extreme Heat"
    }
  ];

  return (
    <div className="min-h-screen bg-background overflow-x-hidden">
      <Header />
      
      {/* Epic Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat scale-110 animate-pulse-slow"
          style={{ backgroundImage: `url(${heroImage})` }}
        >
          <div className="absolute inset-0 bg-black/70" />
          <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-transparent to-black/80" />
        </div>
        
        {/* Floating Elements */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-20 left-10 w-4 h-4 bg-primary rounded-full animate-float opacity-60" />
          <div className="absolute top-40 right-20 w-6 h-6 bg-primary rounded-full animate-bounce-slow opacity-40" />
          <div className="absolute bottom-40 left-20 w-3 h-3 bg-primary rounded-full animate-pulse-slow opacity-80" />
          <div className="absolute top-1/3 right-1/4 w-2 h-2 bg-primary rounded-full animate-float opacity-50" style={{ animationDelay: '2s' }} />
        </div>
        
        <div className="relative z-10 container mx-auto px-4 lg:px-8 text-center">
          <div className={`max-w-6xl mx-auto transition-all duration-2000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-20'}`}>
            <Badge className="mb-8 bg-gradient-luxury text-black px-8 py-3 text-lg font-black animate-shimmer">
              🏆 #1 Wings Restaurant 2024
            </Badge>
            
            <h1 className="hero-title mb-8 animate-fade-in">
              <span className="block luxury-text">RULING FLAVORS</span>
              <span className="block text-primary text-8xl md:text-10xl animate-glow">ONE BITE</span>
              <span className="block luxury-text">AT A TIME</span>
            </h1>
            
            <p className="hero-subtitle mb-12 max-w-4xl mx-auto animate-slide-up">
              Enter a realm where culinary artistry meets explosive flavors. Every wing tells a story of 
              passion, tradition, and revolutionary taste innovation that will forever change your 
              perception of what perfect wings can be.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16 animate-scale-in">
              <Button asChild className="btn-luxury text-xl px-16 py-8 group">
                <Link to="/menu">
                  <Sparkles className="mr-3 h-6 w-6 group-hover:animate-spin" />
                  EXPLORE MENU
                  <ArrowRight className="ml-3 h-6 w-6 group-hover:translate-x-2 transition-transform duration-300" />
                </Link>
              </Button>
              <Button asChild variant="outline" className="btn-secondary text-xl px-16 py-8 group">
                <Link to="/reservations">
                  <Crown className="mr-3 h-6 w-6 group-hover:animate-bounce" />
                  RESERVE TABLE
                </Link>
              </Button>
            </div>

            {/* Video Play Button */}
            <div className="animate-float">
              <button className="group bg-black/30 backdrop-blur-lg border-2 border-primary/50 rounded-full p-8 hover:border-primary hover:scale-110 transition-all duration-500">
                <Play className="h-12 w-12 text-primary group-hover:scale-125 transition-transform duration-300" fill="currentColor" />
              </button>
              <p className="text-muted-foreground mt-4 font-medium">Watch Our Story</p>
            </div>
          </div>
        </div>
        
        {/* Scroll Indicator */}
        <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce-slow">
          <div className="w-6 h-10 border-2 border-primary rounded-full flex justify-center">
            <div className="w-1 h-3 bg-primary rounded-full mt-2 animate-pulse-slow" />
          </div>
        </div>
      </section>

      {/* Revolutionary Stats Section */}
      <section className="py-20 bg-gradient-card relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-spotlight" />
        
        <div className="container mx-auto px-4 lg:px-8 relative z-10">
          <div className="text-center mb-16">
            <Badge className="mb-6 bg-primary/20 text-primary px-6 py-2 text-sm font-bold">
              ACHIEVEMENTS
            </Badge>
            <h2 className="section-title mb-6 animate-fade-in">
              Legendary <span className="luxury-text">Numbers</span>
            </h2>
          </div>
          
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div 
                key={stat.label}
                className={`text-center animate-scale-in hover-expand ${statsVisible ? 'opacity-100' : 'opacity-0'}`}
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="mb-6 flex justify-center">
                  <div className="bg-gradient-luxury p-6 rounded-full shadow-luxury animate-rotate-slow">
                    <stat.icon className="h-10 w-10 text-black" />
                  </div>
                </div>
                <div className="text-5xl md:text-6xl font-black luxury-text mb-4 animate-glow">
                  {stat.number}
                </div>
                <p className="text-muted-foreground font-medium text-lg">{stat.label}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Revolutionary Features */}
      <section className="py-32 bg-background">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-20">
            <Badge className="mb-6 bg-primary/20 text-primary px-6 py-2 text-sm font-bold">
              EXCELLENCE
            </Badge>
            <h2 className="section-title mb-8">
              Why We <span className="luxury-text">Dominate</span>
            </h2>
            <p className="text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
              We don't just serve wings. We orchestrate flavor symphonies that create 
              unforgettable memories and redefine culinary expectations.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            {features.map((feature, index) => (
              <Card 
                key={feature.title} 
                className="card-feature hover-luxury group overflow-hidden animate-fade-in"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="relative">
                  <div className={`absolute inset-0 bg-gradient-to-r ${feature.color} opacity-10 group-hover:opacity-20 transition-opacity duration-500`} />
                  <div className="relative z-10">
                    <div className="mb-8 flex justify-start">
                      <div className={`bg-gradient-to-r ${feature.color} p-4 rounded-2xl shadow-luxury group-hover:scale-110 transition-transform duration-500`}>
                        <feature.icon className="h-8 w-8 text-white" />
                      </div>
                    </div>
                    <h3 className="text-3xl font-playfair font-bold mb-6 group-hover:text-primary transition-colors duration-300">
                      {feature.title}
                    </h3>
                    <p className="text-muted-foreground text-lg leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Spectacular Cuisine Showcase */}
      <section className="py-32 bg-gradient-card">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-20">
            <Badge className="mb-6 bg-primary/20 text-primary px-6 py-2 text-sm font-bold">
              SIGNATURE CREATIONS
            </Badge>
            <h2 className="section-title mb-8">
              Culinary <span className="luxury-text">Masterpieces</span>
            </h2>
            <p className="text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
              Each dish is a carefully orchestrated symphony of flavors, textures, and aromas 
              designed to create an unforgettable culinary journey.
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {cuisineHighlights.map((item, index) => (
              <div 
                key={item.title} 
                className="group cursor-pointer animate-fade-in hover-luxury"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="relative overflow-hidden rounded-3xl mb-8 shadow-premium group-hover:shadow-luxury transition-all duration-700">
                  <img 
                    src={item.image} 
                    alt={item.title}
                    className="w-full h-80 object-cover transition-transform duration-700 group-hover:scale-125"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
                  
                  {/* Overlays */}
                  <div className="absolute top-6 left-6 flex gap-3">
                    {item.popular && (
                      <Badge className="bg-gradient-luxury text-black font-black px-4 py-2 animate-pulse-slow">
                        🔥 POPULAR
                      </Badge>
                    )}
                    {item.spicy && (
                      <Badge className="bg-red-500 text-white font-bold px-4 py-2">
                        🌶️ {item.spicy}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="absolute bottom-6 left-6 right-6">
                    <div className="bg-gradient-luxury text-black px-6 py-3 rounded-full font-black text-lg mb-4 text-center">
                      {item.price}
                    </div>
                  </div>
                </div>
                
                <h3 className="text-3xl font-playfair font-bold mb-4 group-hover:text-primary transition-colors duration-300">
                  {item.title}
                </h3>
                <p className="text-muted-foreground text-lg leading-relaxed mb-6">
                  {item.description}
                </p>
                <Button asChild variant="ghost" className="btn-ghost group-hover:text-primary text-lg">
                  <Link to="/menu">
                    Discover More
                    <ArrowRight className="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-2" />
                  </Link>
                </Button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Epic Testimonials */}
      <section className="py-32 bg-background">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-20">
            <Badge className="mb-6 bg-primary/20 text-primary px-6 py-2 text-sm font-bold">
              TESTIMONIALS
            </Badge>
            <h2 className="section-title mb-8">
              What Legends <span className="luxury-text">Say</span>
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
            {testimonials.map((testimonial, index) => (
              <Card 
                key={testimonial.name}
                className="card-testimonial hover-expand animate-fade-in"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="mb-8">
                  <Quote className="h-12 w-12 text-primary opacity-50 mb-6" />
                  <p className="text-lg leading-relaxed text-muted-foreground italic">
                    "{testimonial.content}"
                  </p>
                </div>
                
                <div className="flex items-center gap-6">
                  <img 
                    src={testimonial.image} 
                    alt={testimonial.name}
                    className="w-16 h-16 rounded-full object-cover border-2 border-primary"
                  />
                  <div>
                    <h4 className="font-bold text-xl">{testimonial.name}</h4>
                    <p className="text-primary font-medium">{testimonial.role}</p>
                    <div className="flex mt-2">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-primary fill-current" />
                      ))}
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Behind the Scenes */}
      <section className="py-32 bg-gradient-card">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-20 items-center">
            <div className="animate-fade-in">
              <Badge className="mb-6 bg-primary/20 text-primary px-6 py-2 text-sm font-bold">
                CRAFTSMANSHIP
              </Badge>
              <h2 className="section-title mb-8">
                The Art of <span className="luxury-text">Perfection</span>
              </h2>
              <p className="text-xl text-muted-foreground leading-relaxed mb-8">
                Step into our culinary laboratory where master chefs transform premium ingredients 
                into works of art. Every wing undergoes a 72-hour preparation process that ensures 
                maximum flavor penetration and perfect texture.
              </p>
              <div className="space-y-6 mb-10">
                <div className="flex items-center gap-4">
                  <div className="bg-primary p-3 rounded-full">
                    <Clock className="h-6 w-6 text-black" />
                  </div>
                  <div>
                    <h4 className="font-bold text-lg">72-Hour Marination</h4>
                    <p className="text-muted-foreground">Deep flavor infusion process</p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="bg-primary p-3 rounded-full">
                    <Flame className="h-6 w-6 text-black" />
                  </div>
                  <div>
                    <h4 className="font-bold text-lg">Perfect Temperature Control</h4>
                    <p className="text-muted-foreground">Precision cooking for optimal texture</p>
                  </div>
                </div>
              </div>
              <Button asChild className="btn-luxury text-lg">
                <Link to="/story">
                  <ChefHat className="mr-3 h-5 w-5" />
                  DISCOVER OUR PROCESS
                </Link>
              </Button>
            </div>
            
            <div className="animate-slide-up">
              <div className="relative group">
                <img 
                  src={cookingImage} 
                  alt="Cooking Process"
                  className="w-full h-[500px] object-cover rounded-3xl shadow-premium group-hover:shadow-luxury transition-all duration-700 hover-lift"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent rounded-3xl" />
                <div className="absolute bottom-8 left-8 right-8 text-center">
                  <p className="text-white font-bold text-xl">Watch the Magic Happen</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Index;

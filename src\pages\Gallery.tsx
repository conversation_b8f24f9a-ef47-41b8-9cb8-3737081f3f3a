import { useState } from "react";
import { X, ZoomIn } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import wingsImage from "@/assets/wings-buffalo.jpg";
import burgerImage from "@/assets/burger-deluxe.jpg";
import interiorImage from "@/assets/restaurant-interior.jpg";
import heroImage from "@/assets/hero-wings.jpg";

const Gallery = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const galleryImages = [
    {
      id: 1,
      src: heroImage,
      alt: "Signature Wings",
      category: "food"
    },
    {
      id: 2,
      src: wingsImage,
      alt: "Buffalo Wings",
      category: "food"
    },
    {
      id: 3,
      src: burgerImage,
      alt: "Gourmet Burger",
      category: "food"
    },
    {
      id: 4,
      src: interiorImage,
      alt: "Restaurant Interior",
      category: "interior"
    },
    {
      id: 5,
      src: wingsImage,
      alt: "Wing Platter",
      category: "food"
    },
    {
      id: 6,
      src: interiorImage,
      alt: "Dining Area",
      category: "interior"
    },
    {
      id: 7,
      src: heroImage,
      alt: "Fresh Wings",
      category: "food"
    },
    {
      id: 8,
      src: burgerImage,
      alt: "Burger Collection",
      category: "food"
    }
  ];

  const openLightbox = (imageSrc: string) => {
    setSelectedImage(imageSrc);
    document.body.style.overflow = 'hidden';
  };

  const closeLightbox = () => {
    setSelectedImage(null);
    document.body.style.overflow = 'unset';
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-gradient-dark">
        <div className="container mx-auto px-4 lg:px-8 text-center">
          <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-fade-in">
            Our <span className="text-primary">Gallery</span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto animate-slide-up">
            Take a visual journey through our culinary creations, restaurant ambiance, 
            and the experiences that make King Ping Wings special.
          </p>
        </div>
      </section>

      {/* Gallery Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {galleryImages.map((image, index) => (
              <div 
                key={image.id}
                className="group relative overflow-hidden rounded-xl cursor-pointer animate-fade-in hover-lift"
                style={{ animationDelay: `${index * 0.1}s` }}
                onClick={() => openLightbox(image.src)}
              >
                <img 
                  src={image.src} 
                  alt={image.alt}
                  className="w-full h-64 object-cover transition-all duration-500 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <div className="bg-white/20 backdrop-blur-sm rounded-full p-3">
                    <ZoomIn className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                  <h3 className="text-white font-semibold">{image.alt}</h3>
                  <p className="text-white/80 text-sm capitalize">{image.category}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Lightbox */}
      {selectedImage && (
        <div 
          className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4 animate-fade-in"
          onClick={closeLightbox}
        >
          <button
            className="absolute top-6 right-6 text-white hover:text-primary transition-colors duration-300 z-10"
            onClick={closeLightbox}
          >
            <X size={32} />
          </button>
          <img 
            src={selectedImage} 
            alt="Gallery Image"
            className="max-w-full max-h-full object-contain animate-scale-in"
            onClick={(e) => e.stopPropagation()}
          />
        </div>
      )}

      <Footer />
    </div>
  );
};

export default Gallery;
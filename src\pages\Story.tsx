import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Card } from "@/components/ui/card";
import { Heart, Users, Award, Clock } from "lucide-react";
import interiorImage from "@/assets/restaurant-interior.jpg";

const Story = () => {
  const milestones = [
    {
      year: "2002",
      title: "The Beginning",
      description: "Started as a small family kitchen with a passion for perfect wings"
    },
    {
      year: "2008",
      title: "First Location",
      description: "Opened our first restaurant location serving the community"
    },
    {
      year: "2015",
      title: "Award Recognition",
      description: "Won 'Best Wings in the City' award three years running"
    },
    {
      year: "2024",
      title: "Today",
      description: "Serving thousands of happy customers with the same family recipes"
    }
  ];

  const values = [
    {
      icon: Heart,
      title: "Passion",
      description: "Every wing is crafted with love and dedication to flavor perfection"
    },
    {
      icon: Users,
      title: "Family",
      description: "We treat every customer like family, creating memories one meal at a time"
    },
    {
      icon: Award,
      title: "Quality",
      description: "Never compromising on ingredients, freshness, or preparation standards"
    },
    {
      icon: Clock,
      title: "Tradition",
      description: "Preserving time-honored recipes while embracing culinary innovation"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-gradient-dark">
        <div className="container mx-auto px-4 lg:px-8 text-center">
          <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-fade-in">
            Our <span className="text-primary">Story</span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto animate-slide-up">
            From humble beginnings to culinary excellence - discover the journey that 
            shaped King Ping Wings into the beloved destination it is today.
          </p>
        </div>
      </section>

      {/* Story Content */}
      <section className="py-16">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
            <div className="animate-fade-in">
              <h2 className="text-4xl font-playfair font-bold mb-6">
                Where It All <span className="text-primary">Began</span>
              </h2>
              <div className="space-y-6 text-lg text-muted-foreground leading-relaxed">
                <p>
                  In 2002, the Rodriguez family had a simple dream: to share their grandmother's 
                  legendary wing recipes with the world. What started in Maria Rodriguez's kitchen 
                  as Sunday family gatherings soon became the foundation of something extraordinary.
                </p>
                <p>
                  Maria's secret wasn't just in her spice blends or cooking techniques—it was in 
                  her belief that food should bring people together. Every wing was seasoned with 
                  love, every sauce perfected through generations of family tradition.
                </p>
                <p>
                  When her grandson Miguel convinced her to open the first King Ping Wings location, 
                  they had no idea they were about to start a culinary legacy that would span decades 
                  and touch thousands of lives.
                </p>
              </div>
            </div>
            <div className="animate-slide-up">
              <img 
                src={interiorImage} 
                alt="Restaurant Story"
                className="w-full h-96 object-cover rounded-2xl shadow-premium hover-lift"
              />
            </div>
          </div>

          {/* Timeline */}
          <div className="mb-20">
            <h2 className="text-4xl font-playfair font-bold text-center mb-12">
              Our <span className="text-primary">Journey</span>
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {milestones.map((milestone, index) => (
                <Card 
                  key={milestone.year}
                  className="card-premium text-center p-6 animate-fade-in hover-lift"
                  style={{ animationDelay: `${index * 0.2}s` }}
                >
                  <div className="text-3xl font-playfair font-bold text-primary mb-4">
                    {milestone.year}
                  </div>
                  <h3 className="text-xl font-semibold mb-3">{milestone.title}</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {milestone.description}
                  </p>
                </Card>
              ))}
            </div>
          </div>

          {/* Values */}
          <div className="bg-secondary rounded-2xl p-12 mb-20">
            <h2 className="text-4xl font-playfair font-bold text-center mb-12">
              Our <span className="text-primary">Values</span>
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {values.map((value, index) => (
                <div 
                  key={value.title}
                  className="text-center animate-fade-in"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="mb-6 flex justify-center">
                    <div className="bg-primary/10 p-4 rounded-full">
                      <value.icon className="h-8 w-8 text-primary" />
                    </div>
                  </div>
                  <h3 className="text-xl font-playfair font-semibold mb-4">{value.title}</h3>
                  <p className="text-muted-foreground leading-relaxed">{value.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Family Legacy */}
          <div className="text-center">
            <h2 className="text-4xl font-playfair font-bold mb-6">
              The <span className="text-primary">Legacy Continues</span>
            </h2>
            <div className="max-w-4xl mx-auto">
              <p className="text-lg text-muted-foreground leading-relaxed mb-6">
                Today, King Ping Wings remains a family-owned business, with the third generation 
                now learning the recipes and values that started it all. We've grown from that 
                small kitchen to serving thousands, but our commitment remains unchanged.
              </p>
              <p className="text-lg text-muted-foreground leading-relaxed mb-8">
                Every wing we serve carries the spirit of Maria's original vision: bringing people 
                together through exceptional food, genuine hospitality, and the belief that a 
                great meal shared is a memory made.
              </p>
              <div className="bg-primary/10 rounded-xl p-8 border border-primary/20">
                <p className="text-xl font-playfair italic text-primary">
                  "Food is love made visible. When you eat our wings, you're tasting 
                  generations of family tradition and care."
                </p>
                <p className="text-muted-foreground mt-4">— Maria Rodriguez, Founder</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Story;